#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

bool isPrime(long long n) {
    if (n < 2) return false;
    if (n % 2 == 0) return (n == 2);
    for (long long i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int T;
    cin >> T;
    while (T--) {
        long long X, Y;
        cin >> X >> Y;
        vector<long long> answer;
        bool found = false;

        // Thử tất cả các giá trị Z có thể (brute force trong phạm vi hợp lý)
        for (long long Z = 2; Z <= 1000000 && !found; Z++) {
            if (!isPrime(Z)) continue;

            // Tính A và B từ X, Y, Z
            long long A = X ^ Z;
            long long B = Y ^ Z;

            // Kiểm tra điều kiện
            if (A > 1 && B > 1 &&
                A != B && B != Z && A != Z &&
                isPrime(A) && isPrime(B)) {
                answer = {A, B, Z};
                found = true;
            }
        }

        if (found) {
            sort(answer.begin(), answer.end());
            cout << answer[0] << " " << answer[1] << " " << answer[2] << endl;
        } else {
            cout << "-1" << endl; // Không tìm được
        }
    }
    return 0;
}

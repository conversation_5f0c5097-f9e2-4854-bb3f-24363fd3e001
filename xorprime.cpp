#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

bool isPrime(long long n) {
    if (n < 2) return false;
    if (n % 2 == 0) return (n == 2);
    for (long long i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int T;
    cin >> T;
    while (T--) {
        long long X, Y;
        cin >> X >> Y;

        // Từ X = A ⊕ B, Y = B ⊕ C, Z = C ⊕ A
        // Ta có: X ⊕ Y ⊕ Z = 0, nên Z = X ⊕ Y
        long long Z = X ^ Y;

        // Công thức toán học chính xác cho hệ phương trình XOR:
        // Từ hệ: X = A⊕B, Y = B⊕C, Z = C⊕A
        // Giải được: A = (Y⊕Z)/2, B = (X⊕Z)/2, C = (X⊕Y)/2
        // Nhưng với XOR, công thức đúng là:
        long long A = (Y ^ Z) >> 1;
        long long B = (X ^ Z) >> 1;
        long long C = (X ^ Y) >> 1;

        // Kiểm tra tính hợp lệ
        if (A > 1 && B > 1 && C > 1 &&
            A != B && B != C && A != C &&
            isPrime(A) && isPrime(B) && isPrime(C) &&
            (A ^ B) == X && (B ^ C) == Y && (C ^ A) == Z) {

            vector<long long> answer = {A, B, C};
            sort(answer.begin(), answer.end());
            cout << answer[0] << " " << answer[1] << " " << answer[2] << endl;
        } else {
            // Nếu công thức trực tiếp không work, dùng brute force
            vector<long long> answer;
            bool found = false;

            for (long long a = 2; a <= 100000 && !found; a++) {
                if (!isPrime(a)) continue;

                long long b = X ^ a;  // Từ X = A ⊕ B
                long long c = Y ^ b;  // Từ Y = B ⊕ C

                if ((c ^ a) == Z && b > 1 && c > 1 &&
                    a != b && b != c && a != c &&
                    isPrime(b) && isPrime(c)) {
                    answer = {a, b, c};
                    found = true;
                }
            }

            if (found) {
                sort(answer.begin(), answer.end());
                cout << answer[0] << " " << answer[1] << " " << answer[2] << endl;
            } else {
                cout << "-1" << endl;
            }
        }
    }
    return 0;
}

#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

bool isPrime(long long n) {
    if (n < 2) return false;
    if (n % 2 == 0) return (n == 2);
    for (long long i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

int main() {
    ios::sync_with_stdio(0);
    cin.tie(0);

    int T;
    cin >> T;
    while (T--) {
        long long X, Y;
        cin >> X >> Y;
        long long Z = X ^ Y;

        vector<array<long long,3>> perms = {
            {X ^ Y, X ^ Z, Y ^ Z},
            {X ^ Y, Y ^ Z, X ^ Z},
            {X ^ Z, X ^ Y, Y ^ Z},
            {X ^ Z, Y ^ Z, X ^ Y},
            {Y ^ Z, X ^ Y, X ^ Z},
            {Y ^ Z, X ^ Z, X ^ Y}
        };

        vector<long long> ans;
        for (auto &p : perms) {
            long long a = p[0], b = p[1], c = p[2];
            if (a > 1 && b > 1 && c > 1 &&
                a != b && b != c && a != c &&
                isPrime(a) && isPrime(b) && isPrime(c)) {
                ans = {a, b, c};
                break;
            }
        }

        sort(ans.begin(), ans.end());
        cout << ans[0] << " " << ans[1] << " " << ans[2] << endl;
    }
    return 0;
}

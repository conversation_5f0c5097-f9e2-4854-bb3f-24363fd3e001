#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

// Hàm kiểm tra số nguyên tố (dùng <PERSON>-<PERSON> hoặc đơn giản trial division)
// Vì A, B, C < 2^30 ~ 1e9, dùng sqrt là được.
bool isPrime(long long n) {
    if (n < 2) return false;
    if (n % 2 == 0) return (n == 2);
    for (long long i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int T;
    cin >> T;
    while (T--) {
        long long X, Y;
        cin >> X >> Y;
        long long Z = X ^ Y; // vì X ^ Y ^ Z = 0

        // Tập hợp ứng viên (A,B,C)
        vector<long long> candidates;

        // 6 cách gán hoán vị
        vector<array<long long,3>> perms = {
            {X ^ Y, X ^ Z, Y ^ Z},
            {X ^ Y, Y ^ Z, X ^ Z},
            {X ^ Z, X ^ Y, Y ^ Z},
            {X ^ Z, Y ^ Z, X ^ Y},
            {Y ^ Z, X ^ Y, X ^ Z},
            {Y ^ Z, X ^ Z, X ^ Y}
        };

        vector<long long> answer;

        for (auto &p : perms) {
            long long a = p[0], b = p[1], c = p[2];
            // kiểm tra điều kiện
            if (a > 1 && b > 1 && c > 1 &&
                a != b && b != c && a != c &&
                isPrime(a) && isPrime(b) && isPrime(c)) {
                answer = {a, b, c};
                break;
            }
        }

        sort(answer.begin(), answer.end());
        cout << answer[0] << " " << answer[1] << " " << answer[2] << endl;
    }
    return 0;
}

#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

bool isPrime(long long n) {
    if (n < 2) return false;
    if (n % 2 == 0) return (n == 2);
    for (long long i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int T;
    cin >> T;
    while (T--) {
        long long X, Y;
        cin >> X >> Y;

        // Từ X = A ⊕ B, Y = B ⊕ C, Z = C ⊕ A
        // Ta có: X ⊕ Y ⊕ Z = 0, nên Z = X ⊕ Y
        long long Z = X ^ Y;

        // Từ hệ phương trình:
        // A ⊕ B = X
        // B ⊕ C = Y
        // C ⊕ A = Z
        // Giải ra: A = (X ⊕ Z) / 2, B = (X ⊕ Y) / 2, C = (Y ⊕ Z) / 2
        // Nhưng công thức đúng là:
        long long A = (X ^ Z) >> 1;  // Sai! Cần suy luận lại

        // Cách đúng: Từ 3 phương trình XOR
        // A = (X + Z - Y) / 2 trong trường hợp không có carry
        // Nhưng với XOR, ta cần cách khác

        // Thử brute force với phạm vi nhỏ hơn
        vector<long long> answer;
        bool found = false;

        for (long long A = 2; A <= 100000 && !found; A++) {
            if (!isPrime(A)) continue;

            long long B = X ^ A;  // Từ X = A ⊕ B
            long long C = Y ^ B;  // Từ Y = B ⊕ C

            // Kiểm tra Z = C ⊕ A
            if ((C ^ A) == Z && B > 1 && C > 1 &&
                A != B && B != C && A != C &&
                isPrime(B) && isPrime(C)) {
                answer = {A, B, C};
                found = true;
            }
        }

        if (found) {
            sort(answer.begin(), answer.end());
            cout << answer[0] << " " << answer[1] << " " << answer[2] << endl;
        } else {
            cout << "-1" << endl;
        }
    }
    return 0;
}

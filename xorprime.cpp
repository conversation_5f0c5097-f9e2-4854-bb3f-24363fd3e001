#include <bits/stdc++.h>
#include <array>
#define endl '\n'
using namespace std;

bool isPrime(long long n) {
    if (n < 2) return false;
    if (n % 2 == 0) return (n == 2);
    for (long long i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

int main() {
    ios::sync_with_stdio(0);
    cin.tie(0);

    int T;
    cin >> T;
    while (T--) {
        long long X, Y;
        cin >> X >> Y;
        // Từ X = A⊕B, Y = B⊕C, Z = C⊕A
        // Ta có: X⊕Y⊕Z = 0, nên Z = X⊕Y
        long long Z = X ^ Y;

        // Công thức toán học chính xác:
        // Từ hệ phương trình XOR, ta có:
        // A = (Y + Z - X) / 2 (trong arithmetic)
        // Nhưng với XOR, cần dùng bit manipulation

        // Công thức đúng cho XOR:
        // A⊕B⊕C = 0 (từ X⊕Y⊕Z = 0)
        // A = B⊕C, B = A⊕C, C = A⊕B
        // Từ đó: 2A = Y⊕Z (trong bit arithmetic)

        long long A = (Y + Z - X) / 2;
        long long B = (X + Z - Y) / 2;
        long long C = (X + Y - Z) / 2;

        // Kiểm tra tính hợp lệ
        if (A > 1 && B > 1 && C > 1 &&
            A != B && B != C && A != C &&
            isPrime(A) && isPrime(B) && isPrime(C) &&
            (A ^ B) == X && (B ^ C) == Y && (C ^ A) == Z) {

            vector<long long> ans = {A, B, C};
            sort(ans.begin(), ans.end());
            cout << ans[0] << " " << ans[1] << " " << ans[2] << endl;
        } else {
            cout << "-1" << endl;
        }
    }
    return 0;
}
